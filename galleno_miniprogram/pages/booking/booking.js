// booking.js
const util = require('../../utils/util.js')
const PaymentUtils = require('../../utils/payment.js')

Page({
  data: {
    bookingData: {},
    formData: {
      name: '',
      phone: ''
    },
    errors: {},
    agreedToTerms: false,
    canPay: false
  },

  onLoad: function (options) {
    console.log('booking页面加载');

    // 获取预订数据
    const bookingData = wx.getStorageSync('bookingData');
    if (bookingData) {
      this.setData({
        bookingData: bookingData
      });
    } else {
      // 如果没有预订数据，返回上一页
      wx.showModal({
        title: '提示',
        content: '预订信息丢失，请重新选择',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }

    // 尝试获取用户信息
    this.getUserInfo();
  },

  onShow: function () {
    console.log('booking页面显示，当前表单数据:', this.data.formData);
  },

  onReady: function () {
    console.log('booking页面渲染完成，当前表单数据:', this.data.formData);
  },

  // 获取用户信息
  getUserInfo: function () {
    // 从本地存储获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    console.log('获取到的用户信息:', userInfo);

    if (userInfo) {
      const updateData = {
        'formData.name': userInfo.name || '',
        'formData.phone': userInfo.phone || ''
      };



      this.setData(updateData, () => {
        console.log('用户信息设置完成:', this.data.formData);
        this.validateForm();
      });
    } else {
      console.log('没有找到用户信息，使用空值');
      // 确保表单数据初始化
      this.setData({
        'formData.name': '',
        'formData.phone': ''
      });
    }
  },

  // 输入框内容变化
  onInputChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;

    console.log(`输入框变化 - 字段: ${field}, 值: ${value}`);

    // 简化的数据更新方式
    const updateData = {};
    updateData[`formData.${field}`] = value;
    updateData[`errors.${field}`] = '';



    this.setData(updateData, () => {
      console.log(`数据更新完成 - ${field}: ${this.data.formData[field]}`);
    });

    this.validateForm();
  },

  // 输入框获得焦点
  onInputFocus: function (e) {
    const field = e.currentTarget.dataset.field;
    console.log(`输入框获得焦点: ${field}`);

    // 可以在这里添加焦点样式变化
    this.setData({
      [`${field}Focused`]: true
    });
  },

  // 输入框失去焦点
  onInputBlur: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    console.log(`输入框失去焦点: ${field}, 值: ${value}`);

    // 移除焦点状态
    this.setData({
      [`${field}Focused`]: false
    });

    // 再次确保数据更新
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 表单验证
  validateForm: function () {
    const { name, phone } = this.data.formData;
    const errors = {};
    let isValid = true;

    // 姓名验证
    if (!name.trim()) {
      errors.name = '请输入姓名';
      isValid = false;
    } else if (name.trim().length < 2) {
      errors.name = '姓名至少2个字符';
      isValid = false;
    }

    // 手机号验证
    if (!phone.trim()) {
      errors.phone = '请输入手机号';
      isValid = false;
    } else if (!util.validatePhone(phone)) {
      errors.phone = '请输入正确的手机号';
      isValid = false;
    }

    this.setData({
      errors: errors,
      canPay: isValid && this.data.agreedToTerms
    });

    return isValid;
  },

  // 切换条款同意状态
  toggleTerms: function () {
    const agreedToTerms = !this.data.agreedToTerms;
    this.setData({
      agreedToTerms: agreedToTerms,
      canPay: this.validateForm() && agreedToTerms
    });
  },

  // 显示预订条款
  showTerms: function () {
    wx.showModal({
      title: '预订条款',
      content: '1. 预订成功后，定金不予退还\n2. 入住时需出示有效身份证件\n3. 如需取消预订，请提前24小时联系客服\n4. 房间价格可能因市场变化而调整\n5. 特殊需求我们将尽力满足，但不保证一定能够实现',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 前往支付
  proceedToPayment: function () {
    if (!this.data.canPay) {
      if (!this.validateForm()) {
        wx.showToast({
          title: '请完善必填信息',
          icon: 'none'
        });
        return;
      }
      if (!this.data.agreedToTerms) {
        wx.showToast({
          title: '请同意预订条款',
          icon: 'none'
        });
        return;
      }
    }
    // 保存用户信息到本地（只更新姓名和手机号）
    const existingUserInfo = wx.getStorageSync('userInfo') || {};
    const updatedUserInfo = {
      ...existingUserInfo,
      name: this.data.formData.name,
      phone: this.data.formData.phone
    };
    wx.setStorageSync('userInfo', updatedUserInfo);

    // 构建完整的订单数据
    const orderData = {
      ...this.data.bookingData,
      guestInfo: this.data.formData,
      orderTime: new Date().toISOString(),
      // orderId: this.generateOrderId(),
      depositAmount: 0.01,
      status: 'pending'
    };

    // 保存订单数据
    wx.setStorageSync('currentOrder', orderData);

    // 调用微信支付
    this.requestPayment(orderData);
  },

  // 生成订单号
  generateOrderId: function () {
    return util.generateOrderId();
  },

  // 请求支付
  requestPayment: function (orderData) {
    // 构建酒店订单创建请求参数
    const createOrderData = {
      conferenceId: orderData.conferenceId,
      categoryId: orderData.categoryId,
      roomId: orderData.roomId,
      roomType: orderData.roomType,
      roomName: orderData.roomName,
      checkinDate: orderData.checkinDate,
      checkoutDate: orderData.checkoutDate,
      nights: orderData.nights,
      roomPrice: orderData.roomPrice,
      totalAmount: orderData.totalAmount,
      depositAmount: orderData.depositAmount,
      guestName: orderData.guestInfo.name,
      guestPhone: orderData.guestInfo.phone,
      specialRequirements: orderData.specialRequirements || '',
      remark: orderData.remark || ''
    };

    console.log('开始创建酒店订单', createOrderData);

    // 调用支付工具类创建订单并支付
    PaymentUtils.createPayment(createOrderData)
      .then((res) => {
        console.log('微信支付返回成功', res);

        // 更新订单数据，保存后端返回的订单号
        if (res.orderNo) {
          orderData.orderNo = res.orderNo;
        }
        if (res.orderId) {
          orderData.orderId = res.orderId;
        }

        // 使用安全的支付成功处理，进行二次验证
        this.handleSecurePaymentSuccess(orderData, res);
      })
      .catch((err) => {
        console.log('支付失败', err);
        this.handlePaymentFail(err);
      });
  },

  // 安全的支付成功处理
  handleSecurePaymentSuccess: function (orderData, paymentResult) {
    console.log('开始安全支付成功处理，订单数据:', orderData, '支付结果:', paymentResult);

    // 显示处理中状态
    wx.showLoading({
      title: '正在确认支付...',
      mask: true
    });

    const PaymentUtils = require('../../utils/payment.js');

    // 使用安全的支付成功处理器，进行二次验证
    PaymentUtils.securePaymentSuccessHandler(orderData.orderNo, paymentResult)
      .then(updatedOrder => {
        console.log('安全支付验证成功', updatedOrder);
        this.handlePaymentSuccess(orderData, updatedOrder);
      })
      .catch(err => {
        console.error('安全支付验证失败', err);
        // 即使验证失败，也认为支付可能成功了，给用户友好提示
        this.handlePaymentSuccessWithWarning(orderData, err);
      });
  },

  // 支付成功处理
  handlePaymentSuccess: function (orderData, updatedOrder) {
    console.log('处理支付成功，订单数据:', orderData, '更新后订单:', updatedOrder);

    // 更新本地订单数据
    orderData.status = 'paid';
    orderData.paymentStatus = 'paid';
    orderData.paymentTime = new Date().toISOString();
    if (updatedOrder && updatedOrder.orderId) {
      orderData.orderId = updatedOrder.orderId;
    }

    // 保存到本地存储
    wx.setStorageSync('currentOrder', orderData);

    // 添加到订单历史
    let orderHistory = wx.getStorageSync('orderHistory') || [];
    orderHistory.unshift(orderData);
    wx.setStorageSync('orderHistory', orderHistory);

    wx.hideLoading();

    // 跳转到支付成功页面
    wx.redirectTo({
      url: '/pages/payment-success/payment-success'
    });
  },

  // 支付成功但验证有警告的处理
  handlePaymentSuccessWithWarning: function (orderData, error) {
    console.log('支付成功但验证有警告，订单数据:', orderData, '错误:', error);

    wx.hideLoading();

    // 给用户提示可能存在延迟
    wx.showModal({
      title: '支付成功',
      content: '支付已完成，订单状态可能稍有延迟，请稍后查看订单详情',
      showCancel: false,
      success: () => {
        // 更新本地订单数据
        orderData.status = 'paid';
        orderData.paymentStatus = 'paid';
        orderData.paymentTime = new Date().toISOString();

        // 保存到本地存储
        wx.setStorageSync('currentOrder', orderData);

        // 添加到订单历史
        let orderHistory = wx.getStorageSync('orderHistory') || [];
        orderHistory.unshift(orderData);
        wx.setStorageSync('orderHistory', orderHistory);

        // 跳转到支付成功页面
        wx.redirectTo({
          url: '/pages/payment-success/payment-success'
        });
      }
    });
  },

  // 支付失败处理
  handlePaymentFail: function (error) {
    if (error.message === '用户取消支付') {
      // 用户取消支付，跳转到订单待支付页面
      console.log('用户取消支付，跳转到订单待支付页面');

      // 确保订单数据已保存到本地存储
      const currentOrder = wx.getStorageSync('currentOrder');
      if (currentOrder) {
        // 更新订单状态为待支付
        currentOrder.status = 'pending';
        currentOrder.paymentStatus = 'unpaid';
        wx.setStorageSync('currentOrder', currentOrder);
      }

      // 跳转到订单待支付页面
      wx.redirectTo({
        url: '/pages/order-pending/order-pending'
      });
    } else if (error.message === '该房型在所选日期已满房，剩余数量: 0') {
        wx.showModal({
          title: '预订失败',
          content: '该房型在所选日期已满房，请重新选择房型或日期',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
    }
    else {
      // 其他支付错误，显示错误提示
      wx.showModal({
        title: '支付失败',
        content: '支付过程中出现问题，请重试或联系客服',
        showCancel: true,
        cancelText: '重试',
        confirmText: '联系客服',
        success: (res) => {
          if (res.confirm) {
            this.contactService();
          } else if (res.cancel) {
            // 用户选择重试，重新发起支付
            const orderData = wx.getStorageSync('currentOrder');
            if (orderData) {
              this.requestPayment(orderData);
            }
          }
        }
      });
    }
  },

  // 联系客服
  contactService: function () {
    wx.makePhoneCall({
      phoneNumber: '4008889999',
      fail: () => {
        wx.showToast({
          title: '拨打失败',
          icon: 'none'
        });
      }
    });
  },



  // 分享功能
  onShareAppMessage: function () {
    return {
      title: '会议酒店预定 - 预订信息',
      path: '/pages/conference-list/conference-list'
    };
  }
});
