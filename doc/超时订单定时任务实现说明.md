# 超时订单定时任务实现说明

## 功能概述

实现定时任务扫描超过30分钟还是待支付的订单，将状态修改为已取消。该功能确保系统能够自动处理超时未支付的订单，释放房间库存，提高系统的自动化管理能力。

## 实现内容

### 1. 定时任务类实现

#### 文件：`ruoyi-quartz/src/main/java/com/ruoyi/quartz/task/HotelOrderTask.java`

新增酒店订单定时任务类，包含以下功能：

- **无参方法**：`handleTimeoutOrders()` - 使用默认30分钟超时时间
- **带参方法**：`handleTimeoutOrders(String params)` - 支持自定义超时时间

**核心特性**：
- 使用Spring的`@Component`注解，组件名为`hotelOrderTask`
- 注入`IHotelOrderService`服务，复用现有的业务逻辑
- 完善的日志记录，包括任务开始、结束、异常处理
- 支持参数化配置超时时间
- 异常处理机制，确保任务稳定运行

### 2. 数据库配置

#### 文件：`sql/hotel_order_timeout_task.sql`

在`sys_job`表中添加两个定时任务配置：

**任务1：超时订单处理（无参）**
- 任务ID：4
- 任务名称：超时订单处理
- 调用目标：`hotelOrderTask.handleTimeoutOrders`
- Cron表达式：`0 */5 * * * ?`（每5分钟执行一次）
- 状态：正常（启用）

**任务2：超时订单处理（带参数）**
- 任务ID：5
- 任务名称：超时订单处理（带参数）
- 调用目标：`hotelOrderTask.handleTimeoutOrders('30')`
- Cron表达式：`0 */5 * * * ?`（每5分钟执行一次）
- 状态：暂停（备用）

### 3. 复用现有业务逻辑

该定时任务复用了现有的业务逻辑：

- **服务方法**：`IHotelOrderService.handleTimeoutOrders(int timeoutMinutes)`
- **数据查询**：`HotelOrderMapper.selectTimeoutPendingOrders(int timeoutMinutes)`
- **状态更新**：通过`cancelOrder`方法更新订单状态为已取消
- **日志记录**：自动记录订单状态变更日志

## 技术实现

### 1. 查询逻辑

使用现有的SQL查询超时订单：

```sql
SELECT * FROM hotel_order 
WHERE order_status = 'PENDING' 
  AND payment_status = 'UNPAID'
  AND create_time < DATE_SUB(NOW(), INTERVAL #{timeoutMinutes} MINUTE)
ORDER BY create_time ASC
```

### 2. 处理逻辑

对每个超时订单执行以下操作：
1. 调用`cancelOrder`方法
2. 设置订单状态为`CANCELLED`
3. 设置取消时间为当前时间
4. 设置取消原因为"订单超时自动取消"
5. 记录状态变更日志

### 3. 事务管理

- 使用`@Transactional`注解确保数据一致性
- 每个订单的处理都在独立的事务中
- 异常不会影响其他订单的处理

## 配置说明

### 1. Cron表达式

`0 */5 * * * ?` - 每5分钟执行一次

- 秒：0（第0秒）
- 分：*/5（每5分钟）
- 时：*（每小时）
- 日：*（每天）
- 月：*（每月）
- 周：?（不指定）

### 2. 超时时间配置

- **默认值**：30分钟
- **可配置**：通过带参数的任务方法支持自定义
- **参数格式**：字符串形式的数字，如"30"、"60"等

### 3. 任务状态

- **正常（0）**：任务启用，按计划执行
- **暂停（1）**：任务暂停，不会执行

## 部署步骤

### 1. 代码部署

1. 确保`HotelOrderTask.java`文件已添加到项目中
2. 重新编译和部署应用

### 2. 数据库配置

执行SQL脚本：
```bash
mysql -u username -p database_name < sql/hotel_order_timeout_task.sql
```

### 3. 任务管理

在管理后台的"系统管理 -> 定时任务"中：
1. 查看新添加的任务
2. 根据需要启用或暂停任务
3. 可以手动执行任务进行测试

## 监控和维护

### 1. 日志监控

关键日志信息：
- `开始执行超时订单处理任务`
- `处理超时订单数量: {count}`
- `超时订单处理任务执行完成`
- `超时订单处理任务执行异常`

### 2. 性能监控

建议监控指标：
- 任务执行时间
- 处理的订单数量
- 任务执行频率
- 异常发生次数

### 3. 业务监控

- 监控超时订单的数量趋势
- 分析订单超时的原因
- 评估30分钟超时设置的合理性

## 注意事项

1. **时间同步**：确保服务器时间准确，避免时间偏差影响判断
2. **数据库性能**：定时任务会定期查询数据库，注意监控性能影响
3. **并发处理**：任务设置为禁止并发执行，避免重复处理
4. **异常处理**：任务异常不会影响系统其他功能
5. **日志管理**：定期清理日志文件，避免占用过多磁盘空间

## 测试建议

1. **功能测试**：创建测试订单，等待超时后验证是否被正确取消
2. **性能测试**：在有大量订单的情况下测试任务执行性能
3. **异常测试**：模拟数据库异常等情况，验证异常处理机制
4. **时间测试**：验证不同超时时间参数的正确性

## 扩展功能

未来可以考虑的扩展：
1. 支持不同类型订单的不同超时时间
2. 添加邮件或短信通知功能
3. 支持订单超时前的提醒功能
4. 添加更详细的统计报表功能
