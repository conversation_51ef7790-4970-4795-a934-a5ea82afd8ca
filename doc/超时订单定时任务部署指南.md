# 超时订单定时任务部署指南

## 概述

本指南详细说明如何部署和配置超时订单处理定时任务，该任务会每5分钟扫描一次超过30分钟还是待支付的订单，并将其状态修改为已取消。

## 部署步骤

### 1. 代码部署

确保以下文件已正确添加到项目中：

```
ruoyi-quartz/src/main/java/com/ruoyi/quartz/task/HotelOrderTask.java
```

### 2. 数据库配置

执行SQL脚本添加定时任务配置：

```bash
# 连接到数据库
mysql -u [username] -p [database_name]

# 执行SQL脚本
source sql/hotel_order_timeout_task.sql;
```

或者直接执行SQL语句：

```sql
-- 添加超时订单处理定时任务
INSERT INTO sys_job VALUES(
    4, 
    '超时订单处理', 
    'DEFAULT', 
    'hotelOrderTask.handleTimeoutOrders', 
    '0 */5 * * * ?', 
    '3', 
    '1', 
    '0', 
    'admin', 
    NOW(), 
    '', 
    null, 
    '每5分钟扫描超过30分钟还是待支付的订单，将状态修改为已取消'
);
```

### 3. 应用重启

重新编译并重启应用服务器，确保新的定时任务类被加载。

## 配置管理

### 1. 通过管理后台配置

1. 登录管理后台
2. 进入"系统管理" -> "定时任务"
3. 找到"超时订单处理"任务
4. 根据需要进行以下操作：
   - **启用任务**：将状态改为"正常"
   - **暂停任务**：将状态改为"暂停"
   - **修改执行频率**：编辑Cron表达式
   - **立即执行**：点击"执行一次"按钮测试

### 2. 常用Cron表达式

```
0 */5 * * * ?   # 每5分钟执行一次（推荐）
0 */10 * * * ?  # 每10分钟执行一次
0 */3 * * * ?   # 每3分钟执行一次
0 0 */1 * * ?   # 每小时执行一次
```

### 3. 参数配置

如需修改超时时间，可以：

1. **使用带参数的任务**：
   - 启用任务ID为5的"超时订单处理（带参数）"
   - 修改调用目标为：`hotelOrderTask.handleTimeoutOrders('60')`（60分钟超时）

2. **修改代码中的默认值**：
   - 编辑`HotelOrderTask.java`文件
   - 修改`int timeoutMinutes = 30;`为所需值

## 验证和测试

### 1. 功能验证

使用测试SQL脚本验证功能：

```bash
mysql -u [username] -p [database_name] < sql/test_timeout_orders.sql
```

### 2. 手动测试

1. 在管理后台找到"超时订单处理"任务
2. 点击"执行一次"按钮
3. 查看日志输出，确认任务正常执行
4. 检查数据库中超时订单是否被正确取消

### 3. 创建测试订单

```sql
-- 创建一个31分钟前的测试订单
INSERT INTO hotel_order (
    order_no, user_id, openid, conference_id, category_id, room_id, 
    room_type, room_name, checkin_date, checkout_date, nights, 
    room_price, total_amount, deposit_amount, order_status, payment_status,
    guest_name, guest_phone, create_by, create_time, update_time
) VALUES (
    CONCAT('TEST', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s')), 
    1, 'test_timeout_openid', 1, 'TEST01', 1,
    'DELUXE', '测试超时房间', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 1 DAY), 1,
    200.00, 200.00, 50.00, 'PENDING', 'UNPAID',
    '测试用户', '13800000000', 'system',
    DATE_SUB(NOW(), INTERVAL 31 MINUTE),
    DATE_SUB(NOW(), INTERVAL 31 MINUTE)
);
```

## 监控和维护

### 1. 日志监控

关键日志位置：
- 应用日志：`logs/sys-info.log`
- 定时任务日志：`logs/sys-error.log`（如有异常）

关键日志内容：
```
INFO  - 开始执行超时订单处理任务
INFO  - 处理超时订单数量: 2
INFO  - 订单超时自动取消，订单号: HT20250117001
INFO  - 超时订单处理任务执行完成
```

### 2. 数据库监控

定期检查以下查询：

```sql
-- 检查最近处理的超时订单
SELECT COUNT(*) as timeout_orders_today
FROM hotel_order 
WHERE order_status = 'CANCELLED' 
  AND cancel_reason = '订单超时自动取消'
  AND DATE(cancel_time) = CURDATE();

-- 检查当前待处理的超时订单
SELECT COUNT(*) as pending_timeout_orders
FROM hotel_order 
WHERE order_status = 'PENDING' 
  AND payment_status = 'UNPAID'
  AND create_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE);
```

### 3. 性能监控

监控指标：
- 任务执行时间
- 处理的订单数量
- 数据库查询性能
- 系统资源使用情况

## 故障排除

### 1. 任务不执行

**可能原因**：
- 任务状态为"暂停"
- Cron表达式错误
- 应用未重启

**解决方案**：
1. 检查任务状态，确保为"正常"
2. 验证Cron表达式格式
3. 重启应用服务器

### 2. 任务执行异常

**可能原因**：
- 数据库连接问题
- 服务依赖注入失败
- 业务逻辑异常

**解决方案**：
1. 检查应用日志中的异常信息
2. 验证数据库连接
3. 检查`IHotelOrderService`服务是否正常

### 3. 订单未被处理

**可能原因**：
- 时间判断逻辑错误
- 订单状态不符合条件
- 数据库时间设置问题

**解决方案**：
1. 检查服务器时间是否准确
2. 验证订单状态和支付状态
3. 手动执行SQL查询验证

## 安全注意事项

1. **权限控制**：确保只有管理员可以修改定时任务配置
2. **数据备份**：定期备份订单数据，防止误操作
3. **日志审计**：保留定时任务执行日志，便于审计
4. **异常处理**：确保任务异常不会影响系统其他功能

## 性能优化建议

1. **索引优化**：确保`create_time`、`order_status`、`payment_status`字段有适当索引
2. **批量处理**：如果超时订单数量很大，考虑分批处理
3. **执行频率**：根据业务需求调整执行频率，避免过于频繁
4. **资源监控**：监控任务执行对系统资源的影响

## 联系支持

如遇到问题，请提供以下信息：
- 错误日志
- 任务配置截图
- 数据库查询结果
- 系统环境信息
