-- 测试超时订单处理功能
-- 用于验证定时任务是否能正确识别和处理超时订单

-- 1. 查看当前待支付订单
SELECT 
    order_id,
    order_no,
    order_status,
    payment_status,
    create_time,
    TIMESTAMPDIFF(MINUTE, create_time, NOW()) as minutes_elapsed
FROM hotel_order 
WHERE order_status = 'PENDING' 
  AND payment_status = 'UNPAID'
ORDER BY create_time ASC;

-- 2. 查看超过30分钟的待支付订单（这些应该被定时任务处理）
SELECT 
    order_id,
    order_no,
    order_status,
    payment_status,
    create_time,
    TIMESTAMPDIFF(MINUTE, create_time, NOW()) as minutes_elapsed
FROM hotel_order 
WHERE order_status = 'PENDING' 
  AND payment_status = 'UNPAID'
  AND create_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
ORDER BY create_time ASC;

-- 3. 创建一个测试用的超时订单（可选，用于测试）
-- 注意：这会创建一个31分钟前的订单，应该被定时任务处理
/*
INSERT INTO hotel_order (
    order_no, user_id, openid, conference_id, category_id, room_id, 
    room_type, room_name, checkin_date, checkout_date, nights, 
    room_price, total_amount, deposit_amount, order_status, payment_status,
    guest_name, guest_phone, create_by, create_time, update_time
) VALUES (
    CONCAT('TEST', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s')), 
    1, 
    'test_timeout_openid', 
    1, 
    'TEST01', 
    1,
    'DELUXE', 
    '测试超时房间', 
    CURDATE(), 
    DATE_ADD(CURDATE(), INTERVAL 1 DAY), 
    1,
    200.00, 
    200.00, 
    50.00, 
    'PENDING', 
    'UNPAID',
    '测试用户', 
    '13800000000', 
    'system',
    DATE_SUB(NOW(), INTERVAL 31 MINUTE),  -- 31分钟前创建
    DATE_SUB(NOW(), INTERVAL 31 MINUTE)
);
*/

-- 4. 查看定时任务配置
SELECT 
    job_id,
    job_name,
    job_group,
    invoke_target,
    cron_expression,
    status,
    remark
FROM sys_job 
WHERE invoke_target LIKE '%hotelOrderTask%';

-- 5. 查看最近被取消的订单（验证定时任务执行结果）
SELECT 
    order_id,
    order_no,
    order_status,
    payment_status,
    cancel_time,
    cancel_reason,
    create_time,
    TIMESTAMPDIFF(MINUTE, create_time, cancel_time) as processing_time_minutes
FROM hotel_order 
WHERE order_status = 'CANCELLED' 
  AND cancel_reason = '订单超时自动取消'
  AND cancel_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)  -- 最近1小时内取消的
ORDER BY cancel_time DESC;
