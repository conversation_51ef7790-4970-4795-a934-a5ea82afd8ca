package com.ruoyi.web.controller.api;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.MiniAppLoginResult;
import com.ruoyi.common.core.domain.WechatApiResult;
import com.ruoyi.common.core.domain.entity.User;
import com.ruoyi.common.core.domain.request.wechat.JsCodeRequest;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.wechat.MiniAppTokenUtils;
import com.ruoyi.common.utils.wechat.WechatMiniAppUtils;
import com.ruoyi.common.utils.wechat.WechatSessionManager;
import com.ruoyi.system.service.IMiniAppLoginService;
import com.ruoyi.system.service.IUserService;
import com.ruoyi.system.service.IWechatPayService;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序API接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Anonymous
@RestController
@RequestMapping("/api/wechat")
public class WechatApiController {
    @Autowired
    private WechatMiniAppUtils wechatMiniAppUtils;

    @Autowired
    private IMiniAppLoginService miniAppLoginService;

    @Autowired
    private IUserService userService;

    @Autowired
    private IWechatPayService wechatPayService;


    /**
     * 强制刷新微信小程序access_token
     */
    @PostMapping("/refreshAccessToken")
    public AjaxResult refreshAccessToken() {
        try {
            String accessToken = wechatMiniAppUtils.getAccessToken(true);
            if (StringUtils.isNotEmpty(accessToken)) {
                return AjaxResult.success("刷新access_token成功", accessToken);
            } else {
                return AjaxResult.error("刷新access_token失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("刷新access_token异常：" + e.getMessage());
        }
    }

    /**
     * 微信小程序登录
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody @Validated JsCodeRequest jsCodeRequest) {
        String jsCode = jsCodeRequest.getJsCode();
        try {
            if (StringUtils.isEmpty(jsCode)) {
                return AjaxResult.error("jsCode不能为空");
            }

            MiniAppLoginResult result = miniAppLoginService.login(jsCode);
            if (result != null) {
                return AjaxResult.success("登录成功", result);
            } else {
                return AjaxResult.error("登录失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("登录异常：" + e.getMessage());
        }
    }

    /**
     * 微信小程序登录（带用户信息）
     */
    @PostMapping("/loginWithUserInfo")
    public AjaxResult loginWithUserInfo(
            @RequestParam String jsCode,
            @RequestParam(required = false) String nickName,
            @RequestParam(required = false) String avatarUrl,
            @RequestParam(required = false) String gender) {
        try {
            if (StringUtils.isEmpty(jsCode)) {
                return AjaxResult.error("jsCode不能为空");
            }

            MiniAppLoginResult result = miniAppLoginService.loginWithUserInfo(jsCode, nickName, avatarUrl, gender);
            if (result != null) {
                return AjaxResult.success("登录成功", result);
            } else {
                return AjaxResult.error("登录失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("登录异常：" + e.getMessage());
        }
    }

    /**
     * 清除access_token缓存
     */
    @PostMapping("/clearCache")
    public AjaxResult clearCache() {
        try {
            wechatMiniAppUtils.clearAccessTokenCache();
            return AjaxResult.success("清除缓存成功");
        } catch (Exception e) {
            return AjaxResult.error("清除缓存异常：" + e.getMessage());
        }
    }



    /**
     * 微信支付V3版本回调通知
     */
    @PostMapping("/pay/notify")
    public String payNotify(@RequestBody String requestBody, HttpServletRequest request) {
        try {
            // V3版本需要获取请求头信息
            Map<String, String> headers = new HashMap<>();
            headers.put("Wechatpay-Signature", request.getHeader("Wechatpay-Signature"));
            headers.put("Wechatpay-Timestamp", request.getHeader("Wechatpay-Timestamp"));
            headers.put("Wechatpay-Nonce", request.getHeader("Wechatpay-Nonce"));
            headers.put("Wechatpay-Serial", request.getHeader("Wechatpay-Serial"));

            return wechatPayService.handlePayNotify(requestBody, headers);
        } catch (Exception e) {
            return "{\"code\":\"FAIL\",\"message\":\"系统异常\"}";
        }
    }
}
